[{"id": "55045267", "index": 1, "trackId": 477512924, "trackName": "夜的命名术-第01集 想等的人（信我，听完十集会上瘾哦~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477512924", "src": ""}, {"id": "55045267", "index": 2, "trackId": 477512932, "trackName": "夜的命名术-第02集 倒计时（听不懂，坚持十集，会很惊喜的~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477512932", "src": ""}, {"id": "55045267", "index": 3, "trackId": 477512946, "trackName": "夜的命名术-第03集 破碎的世界（听不懂，坚持十集，会很惊喜的~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477512946", "src": ""}, {"id": "55045267", "index": 4, "trackId": 477512950, "trackName": "夜的命名术-第04集 不止一个（听不懂，坚持十集，会很惊喜的~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477512950", "src": ""}, {"id": "55045267", "index": 5, "trackId": 477512958, "trackName": "夜的命名术-第05集 超然的地位（听不懂，坚持十集，会很惊喜的~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477512958", "src": ""}, {"id": "55045267", "index": 6, "trackId": 477512962, "trackName": "夜的命名术-第06集 过河的悍卒（上）（听不懂，坚持十集，会很惊喜的~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477512962", "src": ""}, {"id": "55045267", "index": 7, "trackId": 477512972, "trackName": "夜的命名术-第07集 过河的悍卒（下）（听不懂，坚持十集，会很惊喜的~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477512972", "src": ""}, {"id": "55045267", "index": 8, "trackId": 477512979, "trackName": "夜的命名术-第08集 壹（听不懂，坚持十集，会很惊喜的~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477512979", "src": ""}, {"id": "55045267", "index": 9, "trackId": 477512986, "trackName": "夜的命名术-第09集 影子（听不懂，坚持十集，会很惊喜的~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477512986", "src": ""}, {"id": "55045267", "index": 10, "trackId": 477512993, "trackName": "夜的命名术-第10集 梦魇（听不懂，坚持十集，会很惊喜的~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477512993", "src": ""}, {"id": "55045267", "index": 11, "trackId": 477513000, "trackName": "夜的命名术-第11集 要用火（小伙伴们，订阅，打Call，分享整起来~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477513000", "src": ""}, {"id": "55045267", "index": 12, "trackId": 477513008, "trackName": "夜的命名术-第12章 送别（小伙伴们，订阅，打Call，分享整起来~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477513008", "src": ""}, {"id": "55045267", "index": 13, "trackId": 477513013, "trackName": "夜的命名术-第13集 回归（小伙伴们，订阅，打Call，分享整起来~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477513013", "src": ""}, {"id": "55045267", "index": 14, "trackId": 477513028, "trackName": "夜的命名术-第14集 有人（小伙伴们，订阅，打Call，分享整起来~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477513028", "src": ""}, {"id": "55045267", "index": 15, "trackId": 477513033, "trackName": "夜的命名术-第15集 苟富贵，勿相忘（小伙伴们，订阅，打Call，分享整起来~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477513033", "src": ""}, {"id": "55045267", "index": 16, "trackId": 477513041, "trackName": "夜的命名术-第16集 乌云笼罩（小伙伴们，订阅，打Call，分享整起来~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477513041", "src": ""}, {"id": "55045267", "index": 17, "trackId": 477513053, "trackName": "夜的命名术-第17集 命只有一条（小伙伴们，订阅，打Call，分享整起来~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477513053", "src": ""}, {"id": "55045267", "index": 18, "trackId": 477513064, "trackName": "夜的命名术-第18集 交叉跟踪（小伙伴们，订阅，打Call，分享整起来~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477513064", "src": ""}, {"id": "55045267", "index": 19, "trackId": 477513071, "trackName": "夜的命名术-第19集 神秘组织（小伙伴们，订阅，打Call，分享整起来~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477513071", "src": ""}, {"id": "55045267", "index": 20, "trackId": 477513078, "trackName": "夜的命名术-第20集 限制出行（小伙伴们，订阅，打Call，分享整起来~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477513078", "src": ""}, {"id": "55045267", "index": 21, "trackId": 477513085, "trackName": "《夜的命名术》第21集 穿越者的群聊（小伙伴们，订阅，打Call，分享整起来~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477513085", "src": ""}, {"id": "55045267", "index": 22, "trackId": 477513090, "trackName": "《夜的命名术》第22集 资料片：影子之争（小伙伴们，订阅，打Call，分享整起来~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477513090", "src": ""}, {"id": "55045267", "index": 23, "trackId": 477513096, "trackName": "《夜的命名术》第23集 欢迎来到18号监狱（小伙伴们，订阅，打Call，分享整起来~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477513096", "src": ""}, {"id": "55045267", "index": 24, "trackId": 477513103, "trackName": "《夜的命名术》第24集 不速之客（小伙伴们，订阅，打Call，分享整起来~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477513103", "src": ""}, {"id": "55045267", "index": 25, "trackId": 477513112, "trackName": "《夜的命名术》第25集 世界的中心（小伙伴们，订阅，打Call，分享整起来~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477513112", "src": ""}, {"id": "55045267", "index": 26, "trackId": 477513119, "trackName": "《夜的命名术》第26集 新的梦魇（小伙伴们，订阅，打Call，分享整起来~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477513119", "src": ""}, {"id": "55045267", "index": 27, "trackId": 477513131, "trackName": "《夜的命名术》第27集 经历过痛苦的人生，才会更高等（订阅，打Call，分享整起来~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477513131", "src": ""}, {"id": "55045267", "index": 28, "trackId": 477513138, "trackName": "《夜的命名术》第28集 告密者（小伙伴们，订阅，打Call，分享整起来~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477513138", "src": ""}, {"id": "55045267", "index": 29, "trackId": 477513145, "trackName": "《夜的命名术》第29集 再次回归（小伙伴们，订阅，打Call，分享整起来~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477513145", "src": ""}, {"id": "55045267", "index": 30, "trackId": 477513151, "trackName": "《夜的命名术》第30集 真与假（小伙伴们，订阅，打Call，分享整起来~）", "createTime": "2021-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=477513151", "src": ""}]