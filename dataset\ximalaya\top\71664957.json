[{"id": "71664957", "index": 1, "trackId": 581360679, "trackName": "深空彼岸 第1集 旧土（请先订阅，先养起来哦）", "createTime": "2022-10", "beforeApi": "/revision/play/v1/audio?ptype=1&id=581360679"}, {"id": "71664957", "index": 2, "trackId": 581360681, "trackName": "深空彼岸 第2集 韶华易逝（请先订阅，先养起来哦）", "createTime": "2022-10", "beforeApi": "/revision/play/v1/audio?ptype=1&id=581360681"}, {"id": "71664957", "index": 3, "trackId": 581360683, "trackName": "深空彼岸 第3集 续命项目（请先订阅，先养起来哦）", "createTime": "2022-10", "beforeApi": "/revision/play/v1/audio?ptype=1&id=581360683"}, {"id": "71664957", "index": 4, "trackId": 581360688, "trackName": "深空彼岸 第4集 超自然（请先订阅，先养起来哦）", "createTime": "2022-10", "beforeApi": "/revision/play/v1/audio?ptype=1&id=581360688"}, {"id": "71664957", "index": 5, "trackId": 581360691, "trackName": "深空彼岸 第5集 另有一条路（请先订阅，先养起来哦）", "createTime": "2022-10", "beforeApi": "/revision/play/v1/audio?ptype=1&id=581360691"}, {"id": "71664957", "index": 6, "trackId": 581472396, "trackName": "深空彼岸 第6集 弃若敝履（请先订阅，先养起来哦）", "createTime": "2022-10", "beforeApi": "/revision/play/v1/audio?ptype=1&id=581472396"}, {"id": "71664957", "index": 7, "trackId": 581472404, "trackName": "深空彼岸 第7集 女神（请先订阅，先养起来哦）", "createTime": "2022-10", "beforeApi": "/revision/play/v1/audio?ptype=1&id=581472404"}, {"id": "71664957", "index": 8, "trackId": 581472413, "trackName": "深空彼岸 第8集 列仙不存（请先订阅，先养起来哦）", "createTime": "2022-10", "beforeApi": "/revision/play/v1/audio?ptype=1&id=581472413"}, {"id": "71664957", "index": 9, "trackId": 581472414, "trackName": "深空彼岸 第9集 女神的奶奶（请先订阅，先养起来哦）", "createTime": "2022-10", "beforeApi": "/revision/play/v1/audio?ptype=1&id=581472414"}, {"id": "71664957", "index": 10, "trackId": 581472421, "trackName": "深空彼岸 第10集 聚会（请先订阅，先养起来哦）", "createTime": "2022-10", "beforeApi": "/revision/play/v1/audio?ptype=1&id=581472421"}, {"id": "71664957", "index": 11, "trackId": 581673858, "trackName": "深空彼岸 第11集 同窗（请先订阅，先养起来哦）", "createTime": "2022-10", "beforeApi": "/revision/play/v1/audio?ptype=1&id=581673858"}, {"id": "71664957", "index": 12, "trackId": 581673868, "trackName": "深空彼岸 第12集 向往的地方（请先订阅，先养起来哦）", "createTime": "2022-10", "beforeApi": "/revision/play/v1/audio?ptype=1&id=581673868"}, {"id": "71664957", "index": 13, "trackId": 581673879, "trackName": "深空彼岸 第13集 新术（请先订阅，先养起来哦）", "createTime": "2022-10", "beforeApi": "/revision/play/v1/audio?ptype=1&id=581673879"}, {"id": "71664957", "index": 14, "trackId": 581981543, "trackName": "深空彼岸 第14集 新旧争锋（请先订阅，先养起来哦）", "createTime": "2022-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=581981543"}, {"id": "71664957", "index": 15, "trackId": 581981552, "trackName": "深空彼岸 第15集 超术远超你的想象（请先订阅，先养起来哦）", "createTime": "2022-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=581981552"}, {"id": "71664957", "index": 16, "trackId": 581981557, "trackName": "深空彼岸 第16集 马扎（请先订阅，先养起来哦）", "createTime": "2022-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=581981557"}, {"id": "71664957", "index": 17, "trackId": 581985277, "trackName": "深空彼岸 第17集 温和俯视（请先订阅，先养起来哦）", "createTime": "2022-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=581985277"}, {"id": "71664957", "index": 18, "trackId": 581985283, "trackName": "深空彼岸 第18集 旧术路的尽头（请先订阅，先养起来哦）", "createTime": "2022-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=581985283"}, {"id": "71664957", "index": 19, "trackId": 582428201, "trackName": "深空彼岸 第19集 探险（请先订阅，先养起来哦）", "createTime": "2022-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=582428201"}, {"id": "71664957", "index": 20, "trackId": 582428207, "trackName": "深空彼岸 第20集 目标青城山（请先订阅，先养起来哦）", "createTime": "2022-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=582428207"}, {"id": "71664957", "index": 21, "trackId": 582428213, "trackName": "深空彼岸 第21集 地宫（请先订阅，先养起来哦）", "createTime": "2022-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=582428213"}, {"id": "71664957", "index": 22, "trackId": 583054708, "trackName": "深空彼岸 第22集 羽化（请先订阅，先养起来哦）", "createTime": "2022-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=583054708"}, {"id": "71664957", "index": 23, "trackId": 583054713, "trackName": "深空彼岸 第23集 银色兽皮卷（请先订阅，先养起来哦）", "createTime": "2022-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=583054713"}, {"id": "71664957", "index": 24, "trackId": 583054717, "trackName": "深空彼岸 第24集 截胡（请先订阅，先养起来哦）", "createTime": "2022-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=583054717"}, {"id": "71664957", "index": 25, "trackId": 583486818, "trackName": "深空彼岸 第25集 战利品（请先订阅，先养起来哦）", "createTime": "2022-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=583486818"}, {"id": "71664957", "index": 26, "trackId": 583486823, "trackName": "深空彼岸 第26集 偶遇（请先订阅，先养起来哦）", "createTime": "2022-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=583486823"}, {"id": "71664957", "index": 27, "trackId": 583486830, "trackName": "深空彼岸 第27集 偶遇2（请先订阅，先养起来哦）", "createTime": "2022-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=583486830"}, {"id": "71664957", "index": 28, "trackId": 583491796, "trackName": "深空彼岸 第28集 前女友（加更1）", "createTime": "2022-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=583491796"}, {"id": "71664957", "index": 29, "trackId": 583491801, "trackName": "深空彼岸 第29集 亭亭玉立赵清菡（加更2）", "createTime": "2022-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=583491801"}, {"id": "71664957", "index": 30, "trackId": 583491807, "trackName": "深空彼岸 第30集 小王太猛（加更3）", "createTime": "2022-11", "beforeApi": "/revision/play/v1/audio?ptype=1&id=583491807"}]