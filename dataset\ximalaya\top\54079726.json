[{"id": "54079726", "index": 1, "trackId": 482059823, "trackName": "剑仙在此 - 片花（正文在下边）", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=482059823"}, {"id": "54079726", "index": 2, "trackId": 481908139, "trackName": "0001我想要回去【搜《无耻之徒》新书求订阅】", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908139"}, {"id": "54079726", "index": 3, "trackId": 481908162, "trackName": "0002云梦城最大的祸害【搜《无耻之徒》新书求订阅】", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908162"}, {"id": "54079726", "index": 4, "trackId": 481908163, "trackName": "0003少爷，大事不好了【搜《无耻之徒》新书求订阅】", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908163"}, {"id": "54079726", "index": 5, "trackId": 481908161, "trackName": "0004教习，我想修炼【搜《无耻之徒》新书求订阅】", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908161"}, {"id": "54079726", "index": 6, "trackId": 481908166, "trackName": "0005漂亮女朋友有什么用？【搜《无耻之徒》新书求订阅】", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908166"}, {"id": "54079726", "index": 7, "trackId": 481908173, "trackName": "0006不可思议的功能【搜《无耻之徒》新书求订阅】", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908173"}, {"id": "54079726", "index": 8, "trackId": 481908185, "trackName": "0007我的名字里带着一个忠字【搜《无耻之徒》新书求订阅】", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908185"}, {"id": "54079726", "index": 9, "trackId": 481908198, "trackName": "0008林北辰最缺的是什么【搜《无耻之徒》新书求订阅】", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908198"}, {"id": "54079726", "index": 10, "trackId": 481908204, "trackName": "0009我只好摊牌了【搜《无耻之徒》新书求订阅】", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908204"}, {"id": "54079726", "index": 11, "trackId": 481908209, "trackName": "0010林同学，有话好好说【搜《无耻之徒》新书求订阅】", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908209"}, {"id": "54079726", "index": 12, "trackId": 481908241, "trackName": "0011二十枚银币【搜《无耻之徒》新书求订阅】", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908241"}, {"id": "54079726", "index": 13, "trackId": 481908249, "trackName": "剑仙在此-0012震惊的丁三石（给个满星好评吧⭐⭐⭐⭐）", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908249"}, {"id": "54079726", "index": 14, "trackId": 481908258, "trackName": "剑仙在此-0013林北辰很帅（给个满星好评吧⭐⭐⭐⭐）", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908258"}, {"id": "54079726", "index": 15, "trackId": 481908266, "trackName": "剑仙在此-0014原来是这么充电的（给个满星好评吧⭐⭐⭐⭐）", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908266"}, {"id": "54079726", "index": 16, "trackId": 481908273, "trackName": "剑仙在此-0015我叫吴笑方（给个满星好评吧⭐⭐⭐⭐）", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908273"}, {"id": "54079726", "index": 17, "trackId": 481908284, "trackName": "剑仙在此-0016我最喜欢踩天才（给个满星好评吧⭐⭐⭐⭐）", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908284"}, {"id": "54079726", "index": 18, "trackId": 481908290, "trackName": "剑仙在此-0017考出来个100分（有问题v：dabindj ，每天送红包）", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908290"}, {"id": "54079726", "index": 19, "trackId": 481908299, "trackName": "剑仙在此-0018没错，100就是我（有问题v：dabindj ，每天送红包）", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908299"}, {"id": "54079726", "index": 20, "trackId": 481908305, "trackName": "剑仙在此-0019又提前交卷？（有问题v：dabindj ，每天送红包）", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908305"}, {"id": "54079726", "index": 21, "trackId": 481908315, "trackName": "剑仙在此-0020三科第一的妖孽（有问题v：dabindj ，每天送红包）", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908315"}, {"id": "54079726", "index": 22, "trackId": 481908331, "trackName": "剑仙在此-0021特别观察员（有问题v：dabindj ，每天送红包）", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908331"}, {"id": "54079726", "index": 23, "trackId": 481908342, "trackName": "剑仙在此-0022破纪录的成绩（有问题v：dabindj ，每天送红包）", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908342"}, {"id": "54079726", "index": 24, "trackId": 481908356, "trackName": "剑仙在此-0023爆玄（1）（有问题v：dabindj ，每天送红包）", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908356"}, {"id": "54079726", "index": 25, "trackId": 481908362, "trackName": "剑仙在此-0024爆玄（2）（有问题v：dabindj ，每天送红包）", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908362"}, {"id": "54079726", "index": 26, "trackId": 481908364, "trackName": "剑仙在此-0025无效（有问题v：dabindj ，每天送红包）", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908364"}, {"id": "54079726", "index": 27, "trackId": 481908371, "trackName": "剑仙在此-0026你做的很好（有问题v：dabindj ，每天送红包）", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908371"}, {"id": "54079726", "index": 28, "trackId": 481908377, "trackName": "剑仙在此-0027被针对了（有问题v：dabindj ，每天送红包）", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908377"}, {"id": "54079726", "index": 29, "trackId": 481908376, "trackName": "剑仙在此-0028渣男后援团（有问题v：dabindj ，每天送红包）", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908376"}, {"id": "54079726", "index": 30, "trackId": 481908381, "trackName": "剑仙在此-0029卧槽，无情（有问题v：dabindj ，每天送红包）", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=481908381"}]