const common = require('../common/common')
const config = require('../common/config.json')

const output = './dataset/ximalaya/zhangzhen/'

/**
 * @title 喜马拉雅张震相关内容收集器
 * @description 搜索并收集张震相关的音频内容
 * @site https://www.ximalaya.com/search
 */



/**
 * 搜索张震相关内容 - 使用API接口
 * @param {string} keyword 搜索关键词
 * @param {number} page 页码
 */
async function searchZhangZhen(keyword = '张震', page = 1) {
    const host = 'www.ximalaya.com'
    // 使用移动端API接口
    const searchPath = `/revision/search/main?kw=${encodeURIComponent(keyword)}&page=${page}&spellchecker=true&rows=20&condition=relation&device=iPhone&core=album,sound`

    console.log(`正在搜索: ${keyword}, 第${page}页`)

    try {
        const res = await common.onGetSite({
            host,
            path: searchPath,
            headers: {
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
                'Accept': 'application/json, text/plain, */*',
                'Referer': 'https://www.ximalaya.com/'
            }
        })

        if (res.success) {
            try {
                const data = JSON.parse(res.data)
                const results = []

                // 处理专辑结果
                if (data.data && data.data.album && data.data.album.docs) {
                    data.data.album.docs.forEach(item => {
                        results.push({
                            id: item.id,
                            title: item.title,
                            author: item.nickname,
                            cover: item.cover_url_142,
                            category: item.category_title,
                            playcount: item.play_count,
                            description: item.intro,
                            keyword: keyword,
                            type: 'album',
                            beforeApi: `/revision/play/v1/show?num=1&sort=0&size=30&ptype=0&id=${item.id}`
                        })
                    })
                }

                // 处理单个音频结果
                if (data.data && data.data.sound && data.data.sound.docs) {
                    data.data.sound.docs.forEach(item => {
                        results.push({
                            id: item.album_id,
                            trackId: item.id,
                            title: item.album_title,
                            trackTitle: item.title,
                            author: item.nickname,
                            cover: item.cover_url_142,
                            category: item.category_title,
                            playcount: item.play_count,
                            description: item.intro,
                            keyword: keyword,
                            type: 'sound',
                            beforeApi: `/revision/play/v1/show?num=1&sort=0&size=30&ptype=0&id=${item.album_id}`
                        })
                    })
                }

                console.log(`找到 ${results.length} 个相关结果`)
                return results
            } catch (parseError) {
                console.log('解析搜索结果失败:', parseError)
                console.log('原始响应:', res.data.substring(0, 500))
                return []
            }
        } else {
            console.log('搜索请求失败:', res.error)
            return []
        }
    } catch (error) {
        console.log('搜索出错:', error)
        return []
    }
}

/**
 * 获取音频详细信息
 * @param {Object} info 音频基本信息
 */
async function getAudioDetails(info) {
    const host = 'www.ximalaya.com'
    
    try {
        console.log(`获取详细信息: ${info.title}`)
        
        // 获取音频列表
        const data = await common.onGetSite({ host, path: info.beforeApi })
        const audioData = JSON.parse(data.data)
        
        const audios = audioData.data.tracksAudioPlay?.map(item => {
            return {
                id: info.id,
                index: item?.index,
                trackId: item?.trackId,
                trackName: item?.trackName,
                createTime: item?.createTime,
                duration: item?.duration,
                beforeApi: '/revision/play/v1/audio?ptype=1&id=' + item?.trackId,
            }
        }) || []

        // 获取音频播放地址（限制数量避免过度请求）
        const maxTracks = Math.min(audios.length, 10) // 最多获取10个音频的播放地址
        for (let i = 0; i < maxTracks; i++) {
            const item = audios[i]
            console.log(`获取播放地址: ${item.trackName}`)
            
            try {
                const track = await common.onGetSite({ path: item.beforeApi, host })
                const tracks = JSON.parse(track?.data)
                audios[i].src = tracks?.data?.src
                
                // 添加延迟避免请求过快
                await common.wait(1000)
            } catch (error) {
                console.log(`获取播放地址失败: ${item.trackName}`, error)
            }
        }

        return {
            ...info,
            audios: audios,
            totalTracks: audios.length,
            collectedTracks: maxTracks
        }
    } catch (error) {
        console.log(`获取详细信息失败: ${info.title}`, error)
        return {
            ...info,
            audios: [],
            error: error.message
        }
    }
}

/**
 * 主函数 - 收集张震相关数据
 */
module.exports = async function collectZhangZhen() {
    try {
        const zhangzhen = config.zhangzhen || {}
        const today = common.dateFormat().format('YYYYMMDD')
        
        // 检查是否需要更新（每周更新一次）
        if (zhangzhen.updated && Number(today) - Number(zhangzhen.updated) < 7) {
            console.log('------>: ZhangZhen data is up to date!')
            return
        }

        console.log('开始收集张震相关数据...')
        
        // 搜索关键词列表
        const keywords = ['张震', '张震讲故事', '张震鬼故事']
        const allResults = []
        
        // 搜索每个关键词
        for (const keyword of keywords) {
            // 搜索前3页结果
            for (let page = 1; page <= 3; page++) {
                const results = await searchZhangZhen(keyword, page)
                allResults.push(...results)
                
                // 添加延迟避免请求过快
                await common.wait(2000)
            }
        }
        
        // 去重
        const uniqueResults = allResults.filter((item, index, self) => 
            index === self.findIndex(t => t.id === item.id)
        )
        
        console.log(`共找到 ${uniqueResults.length} 个唯一结果`)
        
        // 获取详细信息
        const detailedResults = []
        for (const result of uniqueResults.slice(0, 20)) { // 限制处理前20个结果
            const detailed = await getAudioDetails(result)
            detailedResults.push(detailed)
            
            // 保存单个结果
            const filename = `${result.id}_${result.title.replace(/[^\w\u4e00-\u9fa5]/g, '_')}.json`
            common.createFile(output + filename, JSON.stringify(detailed, null, 2))
            
            // 添加延迟
            await common.wait(3000)
        }
        
        // 保存汇总信息
        const summary = {
            total: detailedResults.length,
            updated: today,
            keywords: keywords,
            results: detailedResults.map(item => ({
                id: item.id,
                title: item.title,
                author: item.author,
                totalTracks: item.totalTracks,
                collectedTracks: item.collectedTracks
            }))
        }
        
        common.createFile(output + 'summary.json', JSON.stringify(summary, null, 2))
        
        // 更新配置
        zhangzhen.updated = today
        config.zhangzhen = zhangzhen
        common.createFile('./common/config.json', JSON.stringify(config))
        
        console.log('------>: ZhangZhen collection completed!')
        
    } catch (error) {
        console.log('张震数据收集出错:', error)
    }
}
