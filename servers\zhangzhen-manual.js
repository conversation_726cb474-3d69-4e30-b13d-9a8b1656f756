const common = require('../common/common')
const config = require('../common/config.json')

const output = './dataset/ximalaya/zhangzhen/'

/**
 * @title 喜马拉雅张震相关内容收集器 - 手动版本
 * @description 使用已知的张震相关专辑ID来收集数据
 */

// 已知的张震相关专辑ID列表（需要手动添加）
const knownZhangZhenAlbums = [
    // 这些ID需要通过手动搜索喜马拉雅网站获得
    // 示例格式：
    // { id: '12345678', title: '张震讲故事', description: '经典恐怖故事' },
    // { id: '87654321', title: '张震鬼故事', description: '午夜惊魂' }
]

/**
 * 获取专辑详细信息
 * @param {Object} albumInfo 专辑基本信息
 */
async function getAlbumDetails(albumInfo) {
    const host = 'www.ximalaya.com'
    
    try {
        console.log(`获取专辑详细信息: ${albumInfo.title || albumInfo.id}`)
        
        // 获取专辑音频列表
        const beforeApi = `/revision/play/v1/show?num=1&sort=0&size=30&ptype=0&id=${albumInfo.id}`
        const data = await common.onGetSite({ host, path: beforeApi })
        
        if (!data.success) {
            console.log(`获取专辑 ${albumInfo.id} 失败:`, data.error)
            return null
        }
        
        const audioData = JSON.parse(data.data)
        
        if (!audioData.data || !audioData.data.tracksAudioPlay) {
            console.log(`专辑 ${albumInfo.id} 没有音频数据`)
            return null
        }
        
        const audios = audioData.data.tracksAudioPlay.map(item => {
            return {
                index: item?.index,
                trackId: item?.trackId,
                trackName: item?.trackName,
                createTime: item?.createTime,
                duration: item?.duration,
                playCount: item?.playCount,
                beforeApi: '/revision/play/v1/audio?ptype=1&id=' + item?.trackId,
            }
        })

        console.log(`专辑 ${albumInfo.title || albumInfo.id} 包含 ${audios.length} 个音频`)

        // 获取前5个音频的播放地址作为示例
        const maxTracks = Math.min(audios.length, 5)
        for (let i = 0; i < maxTracks; i++) {
            const item = audios[i]
            console.log(`获取播放地址: ${item.trackName}`)
            
            try {
                const track = await common.onGetSite({ path: item.beforeApi, host })
                const tracks = JSON.parse(track?.data)
                audios[i].src = tracks?.data?.src
                
                // 添加延迟避免请求过快
                await common.wait(1000)
            } catch (error) {
                console.log(`获取播放地址失败: ${item.trackName}`, error.message)
            }
        }

        return {
            id: albumInfo.id,
            title: albumInfo.title,
            description: albumInfo.description,
            audios: audios,
            totalTracks: audios.length,
            collectedPlayUrls: maxTracks,
            collectedAt: new Date().toISOString()
        }
    } catch (error) {
        console.log(`获取专辑详细信息失败: ${albumInfo.id}`, error.message)
        return null
    }
}

/**
 * 通过专辑ID直接获取张震相关内容
 * @param {string} albumId 专辑ID
 */
async function getZhangZhenByAlbumId(albumId) {
    console.log(`正在获取专辑: ${albumId}`)
    
    const albumInfo = { id: albumId }
    const details = await getAlbumDetails(albumInfo)
    
    if (details) {
        // 保存专辑数据
        const filename = `album_${albumId}.json`
        common.createFile(output + filename, JSON.stringify(details, null, 2))
        console.log(`已保存专辑数据: ${filename}`)
    }
    
    return details
}

/**
 * 主函数 - 收集张震相关数据
 */
module.exports = async function collectZhangZhenManual() {
    try {
        console.log('开始手动收集张震相关数据...')
        
        // 如果没有预设的专辑ID，提供一些示例ID供测试
        let albumIds = knownZhangZhenAlbums.map(album => album.id)
        
        if (albumIds.length === 0) {
            console.log('没有预设的张震专辑ID')
            console.log('请手动在喜马拉雅网站搜索"张震"相关内容，获取专辑ID')
            console.log('专辑ID可以从URL中获取，例如: https://www.ximalaya.com/album/12345678')
            console.log('然后将ID添加到 knownZhangZhenAlbums 数组中')
            
            // 提供一些可能的测试ID（这些可能不是张震相关的，仅用于测试功能）
            const testIds = ['203355', '3062741'] // 这些是从现有数据中随机选择的ID
            console.log('使用测试ID进行功能验证...')
            albumIds = testIds
        }
        
        const results = []
        
        // 收集每个专辑的数据
        for (const albumId of albumIds) {
            const result = await getZhangZhenByAlbumId(albumId)
            if (result) {
                results.push(result)
            }
            
            // 添加延迟避免请求过快
            await common.wait(3000)
        }
        
        // 保存汇总信息
        const summary = {
            total: results.length,
            updated: common.dateFormat().format('YYYYMMDD'),
            method: 'manual_album_ids',
            results: results.map(item => ({
                id: item.id,
                title: item.title,
                totalTracks: item.totalTracks,
                collectedPlayUrls: item.collectedPlayUrls
            }))
        }
        
        common.createFile(output + 'summary_manual.json', JSON.stringify(summary, null, 2))
        
        console.log(`------>: ZhangZhen manual collection completed! 收集了 ${results.length} 个专辑`)
        
        return results
        
    } catch (error) {
        console.log('张震数据手动收集出错:', error)
        return []
    }
}
