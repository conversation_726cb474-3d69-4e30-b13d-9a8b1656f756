 
name: Dataset application

on:
  workflow_dispatch:
  schedule:
    - cron: '0 1 * * *'

env:
  GITHUB_NAME: forzys
  GITHUB_REPO: dataset 
  GITHUB_EMAIL: <EMAIL> 

jobs:
  build:
    name:  Cron Run Bing
    runs-on: ubuntu-latest 
    steps:
      - uses: actions/checkout@v3
      
      - name: Install and Run
        run: yarn && node app.js 
        
      - name: commit
        run: | 
          git config --local user.email "${{ env.GITHUB_EMAIL }}"
          git config --local user.name "${{ env.GITHUB_NAME }}" 
          git add common/*.json dataset/**/*.json
          git commit -a -m 'update bing' || echo "nothing to commit"
          git push || echo "nothing to push" 