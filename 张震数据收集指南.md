# 喜马拉雅张震数据收集指南

## 概述

本项目已经为您创建了专门收集张震相关音频数据的功能。由于喜马拉雅的搜索API可能有访问限制，我们提供了两种收集方法：

## 方法一：自动搜索收集（推荐尝试）

### 使用方式
```bash
# 运行完整的数据收集（包括张震数据）
npm run start

# 或者单独测试张震数据收集
node test-zhangzhen.js
```

### 特点
- 自动搜索"张震"、"张震讲故事"、"张震鬼故事"等关键词
- 使用喜马拉雅的移动端API接口
- 如果搜索成功，会自动收集相关专辑和音频信息

## 方法二：手动指定专辑ID收集（推荐使用）

### 步骤1：获取张震相关专辑ID

1. 打开喜马拉雅网站：https://www.ximalaya.com/
2. 搜索"张震"或"张震讲故事"
3. 找到相关专辑，点击进入专辑页面
4. 从URL中获取专辑ID，例如：
   - URL: `https://www.ximalaya.com/album/12345678`
   - 专辑ID: `12345678`

### 步骤2：配置专辑ID

编辑文件 `servers/zhangzhen-manual.js`，找到 `knownZhangZhenAlbums` 数组，添加您找到的专辑信息：

```javascript
const knownZhangZhenAlbums = [
    { id: '12345678', title: '张震讲故事', description: '经典恐怖故事' },
    { id: '87654321', title: '张震鬼故事', description: '午夜惊魂' },
    // 添加更多专辑...
]
```

### 步骤3：运行收集

```bash
node test-zhangzhen-manual.js
```

## 收集结果

### 数据存储位置
- 主目录：`dataset/ximalaya/zhangzhen/`
- 专辑数据：`album_[专辑ID].json`
- 汇总信息：`summary_manual.json`

### 数据结构
每个专辑文件包含：
- 专辑基本信息（ID、标题、描述）
- 音频列表（标题、时长、播放次数等）
- 部分音频的播放地址（前5个作为示例）

### 示例数据结构
```json
{
  "id": "12345678",
  "title": "张震讲故事",
  "description": "经典恐怖故事",
  "audios": [
    {
      "index": 1,
      "trackId": "123456",
      "trackName": "第一个故事",
      "duration": 1200,
      "playCount": 50000,
      "src": "https://audio.xmcdn.com/..."
    }
  ],
  "totalTracks": 100,
  "collectedPlayUrls": 5,
  "collectedAt": "2025-08-01T10:30:00.000Z"
}
```

## 注意事项

1. **请求频率限制**：为避免被限制访问，程序在请求之间添加了延迟
2. **播放地址获取**：由于API限制，只收集前5个音频的播放地址作为示例
3. **数据更新**：程序会检查上次更新时间，避免重复收集
4. **错误处理**：如果某些请求失败，程序会继续处理其他数据

## 扩展功能

### 添加到定时收集
如果您想将张震数据收集添加到定时任务中，可以：

1. 编辑 `servers/index.js`
2. 确认 `zhangzhen` 已添加到 `tasks` 数组中
3. 运行 `npm run start` 进行完整的数据收集

### 自定义收集参数
您可以修改以下参数：
- 搜索关键词（在 `servers/zhangzhen.js` 中）
- 收集的音频播放地址数量（默认5个）
- 请求延迟时间（默认1-3秒）

## 故障排除

### 问题1：搜索无结果
- 原因：喜马拉雅API可能有访问限制
- 解决：使用方法二（手动指定专辑ID）

### 问题2：播放地址获取失败
- 原因：API返回HTML而不是JSON
- 影响：不影响专辑和音频列表的收集，只是无法获取播放地址
- 解决：这是正常现象，专辑信息仍然会被正确收集

### 问题3：请求过快被限制
- 原因：请求频率过高
- 解决：程序已内置延迟机制，如仍有问题可增加延迟时间

## 联系支持

如果您在使用过程中遇到问题，请检查：
1. 网络连接是否正常
2. 专辑ID是否正确
3. 控制台输出的错误信息

收集完成后，您可以在 `dataset/ximalaya/zhangzhen/` 目录中查看所有收集到的张震相关数据。
