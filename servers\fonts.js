 

const common = require('../common/common.js')
 
function onGetFont(){

    common.onGetSite({
        host: 'www.fonts.net.cn',
        path: '/commercial-free/fonts-zh-$.html'?.replace('$', 1),
        method: 'GET',
        headers: {
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        },
    }).then((res)=>{ 

        common.formatHtml(res.data, ($)=>{
            const fonts = []
            $('.site_font_list li', '#font-list').each(function () {
                fonts.push({
                    title: $('a.site_font_name', this)?.attr('title'),
                    id: $('a.site_font_name', this)?.attr('font-detail-link'),
                    cover: $('a.site_font_cover>img', this)?.attr('src'),
                })
            })
            return fonts 
        })
 
    })
}
 

module.exports = function main(){
    onGetFont()
    console.log('------>: <PERSON><PERSON> had done!')
};





