[{"id": "44741345", "index": 1, "trackId": 364919053, "trackName": "第001集 伴生空间的十颗蛋（搜：我的治愈系游戏，每天抽188现金红包）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=364919053", "src": ""}, {"id": "44741345", "index": 2, "trackId": 364919059, "trackName": "第002集 永恒炼狱凤凰！（搜：我的治愈系游戏，每天抽188现金红包）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=364919059", "src": ""}, {"id": "44741345", "index": 3, "trackId": 364919069, "trackName": "第003集 所遇非良人，一生既毁（（搜：我的治愈系游戏，每天抽188现金红包）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=364919069", "src": ""}, {"id": "44741345", "index": 4, "trackId": 364919076, "trackName": "第004集 烈火印，母猪上树！（搜：我的治愈系游戏，每天抽188现金红包）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=364919076", "src": ""}, {"id": "44741345", "index": 5, "trackId": 364919082, "trackName": "第005集 目标：天府！（搜：我的治愈系游戏，每天抽188现金红包）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=364919082", "src": ""}, {"id": "44741345", "index": 6, "trackId": 364919093, "trackName": "第006集 吃灵矿的怪物（搜：我的治愈系游戏，每天抽188现金红包）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=364919093", "src": ""}, {"id": "44741345", "index": 7, "trackId": 364919119, "trackName": "第007集 神秘的黑暗臂！（搜：我的治愈系游戏，每天抽188现金红包）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=364919119", "src": ""}, {"id": "44741345", "index": 8, "trackId": 364919132, "trackName": "第008集 独霸听风台！（搜：我的治愈系游戏，每天抽188现金红包）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=364919132", "src": ""}, {"id": "44741345", "index": 9, "trackId": 364926275, "trackName": "第009集 痛打落水狗？（搜：我的治愈系游戏，每天抽188现金红包）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=364926275", "src": ""}, {"id": "44741345", "index": 10, "trackId": 365264069, "trackName": "第010集 离火城地震！（搜：我的治愈系游戏，每天抽188现金红包）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=365264069", "src": ""}, {"id": "44741345", "index": 11, "trackId": 365264072, "trackName": "第011集 哥，我错了！（搜：我的治愈系游戏，每天抽188现金红包）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=365264072", "src": ""}, {"id": "44741345", "index": 12, "trackId": 365264080, "trackName": "第012集 曾经的尊严（搜：我的治愈系游戏，每天抽188现金红包）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=365264080", "src": ""}, {"id": "44741345", "index": 13, "trackId": 365264085, "trackName": "第013集 会是谁呢？（搜：我的治愈系游戏，每天抽188现金红包）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=365264085", "src": ""}, {"id": "44741345", "index": 14, "trackId": 365264089, "trackName": "第014集 细思极恐（搜：我的治愈系游戏，每天抽188现金红包）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=365264089", "src": ""}, {"id": "44741345", "index": 15, "trackId": 365264092, "trackName": "第015集 一月之期（搜：我的治愈系游戏，每天抽188现金红包）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=365264092", "src": ""}, {"id": "44741345", "index": 16, "trackId": 365599144, "trackName": "第016集 萤火（搜：我的治愈系游戏，每天抽188现金红包）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=365599144", "src": ""}, {"id": "44741345", "index": 17, "trackId": 365599153, "trackName": "第017集 我宣布，我遇到爱情了（搜：我的治愈系游戏，每天抽188现金红包）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=365599153", "src": ""}, {"id": "44741345", "index": 18, "trackId": 365599158, "trackName": "第018集 凶残的猿猴（搜：我的治愈系游戏，每天抽188现金红包）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=365599158", "src": ""}, {"id": "44741345", "index": 19, "trackId": 365599167, "trackName": "第019集 棂儿的指甲（搜：我的治愈系游戏，每天抽188现金红包）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=365599167", "src": ""}, {"id": "44741345", "index": 20, "trackId": 365599173, "trackName": "第020集 男人没一个好东西（搜：我的治愈系游戏，每天抽188现金红包）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=365599173", "src": ""}, {"id": "44741345", "index": 21, "trackId": 365599177, "trackName": "《万古第一神》第021集 五阶半生兽", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=365599177", "src": ""}, {"id": "44741345", "index": 22, "trackId": 365936728, "trackName": "《万古第一神》第022集 人不犯我，我不犯人（1更）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=365936728", "src": ""}, {"id": "44741345", "index": 23, "trackId": 365936734, "trackName": "《万古第一神》第023集 霸气的公主殿下！（2更）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=365936734", "src": ""}, {"id": "44741345", "index": 24, "trackId": 365936740, "trackName": "《万古第一神》第024集 棂儿的悄悄话（3更）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=365936740", "src": ""}, {"id": "44741345", "index": 25, "trackId": 365936745, "trackName": "《万古第一神》第025集 湖底的神秘（4更）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=365936745", "src": ""}, {"id": "44741345", "index": 26, "trackId": 365936749, "trackName": "《万古第一神》第026集 进击的小黄鸡（5更）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=365936749", "src": ""}, {"id": "44741345", "index": 27, "trackId": 365936756, "trackName": "《万古第一神》第027集 雷尊府天骄（6更）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=365936756", "src": ""}, {"id": "44741345", "index": 28, "trackId": 366271276, "trackName": "《万古第一神》第028集 背锅侠柳千阳（1更）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=366271276", "src": ""}, {"id": "44741345", "index": 29, "trackId": 366271280, "trackName": "《万古第一神》第029集 青公主的人（2更）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=366271280", "src": ""}, {"id": "44741345", "index": 30, "trackId": 366271291, "trackName": "《万古第一神》第030集 白色圆球（3更）", "createTime": "2020-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=366271291", "src": ""}]