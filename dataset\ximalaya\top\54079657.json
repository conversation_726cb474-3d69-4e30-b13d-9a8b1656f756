[{"id": "54079657", "index": 1, "trackId": 482666320, "trackName": "《吞噬星空》预告【《仙逆》有声剧热播中，欢迎追听】", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=482666320", "src": ""}, {"id": "54079657", "index": 2, "trackId": 482666323, "trackName": "吞噬星空 第1集 武馆新秀【《仙逆》有声剧热播中，欢迎追听】", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=482666323", "src": ""}, {"id": "54079657", "index": 3, "trackId": 482666324, "trackName": "吞噬星空 第2集 黑冠金雕【《仙逆》有声剧热播中，欢迎追听】", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=482666324", "src": ""}, {"id": "54079657", "index": 4, "trackId": 482666409, "trackName": "吞噬星空 第3集 RR病毒【烙铁们，求月票❤️订阅❤评论】", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=482666409", "src": ""}, {"id": "54079657", "index": 5, "trackId": 482666427, "trackName": "吞噬星空 第4集 立志上军校【烙铁们，求月票❤️订阅❤评论】", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=482666427", "src": ""}, {"id": "54079657", "index": 6, "trackId": 482666473, "trackName": "吞噬星空 第5集 头疼病【烙铁们，求月票❤️订阅❤评论】", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=482666473", "src": ""}, {"id": "54079657", "index": 7, "trackId": 483020553, "trackName": "吞噬星空 第6集 高考意外", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=483020553", "src": ""}, {"id": "54079657", "index": 8, "trackId": 483020558, "trackName": "吞噬星空 第7集 意外之喜", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=483020558", "src": ""}, {"id": "54079657", "index": 9, "trackId": 483020559, "trackName": "吞噬星空 第8集 十八岁武者", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=483020559", "src": ""}, {"id": "54079657", "index": 10, "trackId": 483379932, "trackName": "吞噬星空 第9集 你也有今天", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=483379932", "src": ""}, {"id": "54079657", "index": 11, "trackId": 483379934, "trackName": "吞噬星空 第10集 准武者考核", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=483379934", "src": ""}, {"id": "54079657", "index": 12, "trackId": 483379935, "trackName": "吞噬星空 第11集 入微身法", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=483379935", "src": ""}, {"id": "54079657", "index": 13, "trackId": 483700717, "trackName": "吞噬星空 第12集 基因原能", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=483700717", "src": ""}, {"id": "54079657", "index": 14, "trackId": 483700720, "trackName": "吞噬星空 第13集 为父出手", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=483700720", "src": ""}, {"id": "54079657", "index": 15, "trackId": 483700722, "trackName": "吞噬星空 第14集 关进看守所", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=483700722", "src": ""}, {"id": "54079657", "index": 16, "trackId": 484014589, "trackName": "吞噬星空 第15集 买凶下黑手", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=484014589", "src": ""}, {"id": "54079657", "index": 17, "trackId": 484014591, "trackName": "吞噬星空 第16集 看守所的预谋", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=484014591", "src": ""}, {"id": "54079657", "index": 18, "trackId": 484014593, "trackName": "吞噬星空 第17集 无形力量", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=484014593", "src": ""}, {"id": "54079657", "index": 19, "trackId": 484348931, "trackName": "吞噬星空 第18集 暗金色圆球", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=484348931", "src": ""}, {"id": "54079657", "index": 20, "trackId": 484348934, "trackName": "吞噬星空 第19集 扬州浴馆", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=484348934", "src": ""}, {"id": "54079657", "index": 21, "trackId": 484348936, "trackName": "吞噬星空 第20集 血影战刀", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=484348936", "src": ""}, {"id": "54079657", "index": 22, "trackId": 484714396, "trackName": "吞噬星空 第21集 怪物天才", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=484714396", "src": ""}, {"id": "54079657", "index": 23, "trackId": 484714399, "trackName": "吞噬星空 第22集 极限总会馆", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=484714399", "src": ""}, {"id": "54079657", "index": 24, "trackId": 484714400, "trackName": "吞噬星空 第23集 一点小钱", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=484714400", "src": ""}, {"id": "54079657", "index": 25, "trackId": 485141055, "trackName": "吞噬星空 第24集 明月小区", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=485141055", "src": ""}, {"id": "54079657", "index": 26, "trackId": 485141070, "trackName": "吞噬星空 第25集 武者实战考核", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=485141070", "src": ""}, {"id": "54079657", "index": 27, "trackId": 485141083, "trackName": "吞噬星空 第26集 对战野猪", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=485141083", "src": ""}, {"id": "54079657", "index": 28, "trackId": 485431820, "trackName": "吞噬星空 第27集 挖墙角啊", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=485431820", "src": ""}, {"id": "54079657", "index": 29, "trackId": 485431832, "trackName": "吞噬星空 第28集 一堆左耳", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=485431832", "src": ""}, {"id": "54079657", "index": 30, "trackId": 485431837, "trackName": "吞噬星空 第29集 极限之家", "createTime": "2021-12", "beforeApi": "/revision/play/v1/audio?ptype=1&id=485431837", "src": ""}]